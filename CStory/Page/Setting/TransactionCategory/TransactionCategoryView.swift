//
//  TransactionCategorySettings.swift
//  CStory
//
//  Created by NZUE on 2025/1/12.
//

import SwiftData
import SwiftUI

/// 交易类别设置视图
///
/// 该视图用于管理交易的类别设置，包括收入和支出两大类别的管理。
/// 视图采用网格式布局，支持类别的查看和管理：
/// - 类型切换：支出/收入
/// - 主类别网格：以网格形式展示主类别
/// - 子类别网格：展示选中主类别的子类别
///
/// ## 主要功能
/// - 类型管理
///   - 支出类别管理
///   - 收入类别管理
/// - 类别操作
///   - 查看类别详情
///   - 编辑类别信息
///   - 添加新类别
///
/// ## 使用示例
/// ```swift
/// TransactionCategorySettings()
///     .modelContainer(for: TransactionMainCategoryModel.self)
/// ```
struct TransactionCategoryView: View {
  // MARK: - Properties
  @Environment(\.dismiss) private var dismiss
  /// 数据上下文 (仅用于写操作)
  @Environment(\.modelContext) private var modelContext
  /// 导航路径管理器
  @Environment(\.presentationMode) var presentationMode
  /// 路径管理器
  @EnvironmentObject private var pathManager: PathManagerHelper
  /// 集中式数据管理器
  @Environment(\.dataManager) private var dataManager

  /// ViewModel
  @ObservedObject private var viewModel = TransactionCategoryVM()

  /// 用于背景动画的命名空间
  @Namespace private var animation

  /// 触觉反馈管理器
  private let hapticManager = HapticFeedbackManager.shared

  // MARK: - Computed Properties

  /// 根据类型筛选的类别列表
  private var filteredCategories: [TransactionMainCategoryModel] {
    viewModel.filteredCategories(from: dataManager)
  }

  /// 选中的主类别
  private var selectedMainCategory: TransactionMainCategoryModel? {
    viewModel.selectedMainCategory(from: dataManager)
  }

  /// 检查是否存在重复的类别
  private var hasDuplicates: Bool {
    viewModel.hasDuplicates(from: dataManager)
  }

  // MARK: - Body

  var body: some View {
    ZStack {
      VStack(spacing: 0) {
        // 标题栏
        NavigationBarKit(
          viewModel: NavigationBarKitVM(
            title: "分类管理",
            backAction: {
              hapticManager.trigger(.impactLight)
              dismiss()
            },
            rightButton: hasDuplicates
              ? .text(
                "去重",
                action: {
                  viewModel.removeDuplicates(using: modelContext, dataManager: dataManager)
                }
              ) : nil
          ))

        // 类型选择器
        HStack(spacing: 8) {
          ForEach([TransactionType.expense, TransactionType.income], id: \.self) { type in
            Button(action: {
              viewModel.switchType(to: type)
            }) {
              Text(type == .expense ? "支出" : "收入")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(
                  viewModel.selectedType == type ? Color.cWhite : Color.cBlack.opacity(0.6)
                )
                .frame(maxWidth: .infinity)
                .frame(height: 34)
                .background(
                  ZStack {
                    if viewModel.selectedType == type {
                      Color.cAccentBlue
                        .cornerRadius(14)
                        .matchedGeometryEffect(id: "background", in: animation)
                    }
                  }
                )
            }
          }
        }
        .padding(.horizontal, 2)
        .padding(.vertical, 2)
        .background(Color.cWhite)
        .cornerRadius(16)
        .overlay(
          RoundedRectangle(cornerRadius: 16)
            .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
        )
        .padding(.horizontal, 16)
        .padding(.vertical, 12)

        ScrollView(showsIndicators: false) {
          LazyVStack(spacing: 8) {
            ForEach(filteredCategories, id: \.id) { category in
              VStack(spacing: 0) {
                // 主类别行 - 移除所有拖拽功能
                CategorySettingRow(
                  viewModel: CategorySettingRowVM(
                    from: category,
                    isExpanded: viewModel.selectedMainCategoryId == category.id
                      && viewModel.showSubCategories,
                    onTap: {
                      viewModel.handleMainCategoryTap(categoryId: category.id)
                    },
                    onMoreAction: {
                      viewModel.showMainCategoryActions(categoryId: category.id)
                    }
                  )
                )

                // 子类别展开区域
                if viewModel.selectedMainCategoryId == category.id && viewModel.showSubCategories,
                  let subCategories = category.subCategories?.sorted(by: { $0.order < $1.order })
                {
                  SubCategoriesExpandedView(
                    subCategories: subCategories,
                    parentCategory: category,
                    viewModel: viewModel,
                    hapticManager: hapticManager,
                    pathManager: pathManager,
                    modelContext: modelContext,
                    dataManager: dataManager
                  )
                }
              }
            }
          }
          .padding(.horizontal, 16)
          .padding(.top, 12)
          .padding(.bottom, 80)
        }
      }

      // 底部浮动按钮
      FloatingActionButtonView(
        title: "添加主类别",
        action: {
          hapticManager.trigger(.impactLight)
          pathManager.path.append(
            NavigationDestination.createTransactionCategory(
              isMainCategory: true,
              mainCategoryId: nil,
              selectedType: viewModel.selectedType
            )
          )
        },
        style: .primary
      )
    }
    .navigationBarTitleDisplayMode(.inline)
    .navigationBarBackButtonHidden(true)
    .background(Color("C_LightBlue"))
    //    .background(Color.cLightBlue)
    .floatingSheet(
      isPresented: $viewModel.showingActionSheet,
      config: SheetBase(
        maxDetent: .fraction(0.45),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      CategoryActionSheet(
        onEdit: {
          if let categoryId = viewModel.selectedCategoryId {
            pathManager.path.append(
              NavigationDestination.editTransactionCategoryView(
                categoryId: categoryId,
                isMainCategory: viewModel.isSelectedMainCategory
              )
            )
          }
        },
        onSort: {
          // 当点击排序时，根据类别类型跳转到对应的排序页面
          if let categoryId = viewModel.selectedCategoryId {
            if viewModel.isSelectedMainCategory {
              // 主类别排序
              pathManager.path.append(
                NavigationDestination.categorySort(
                  mode: .mainCategory(type: viewModel.selectedType)
                )
              )
            } else {
              // 子类别排序 - 需要找到关联的主类别
              if let subCategory = dataManager.subCategories.first(where: { $0.id == categoryId }) {
                pathManager.path.append(
                  NavigationDestination.categorySort(
                    mode: .subCategory(mainCategoryId: subCategory.mainId)
                  )
                )
              }
            }
          }
        },
        onMigrate: {
          // 当点击迁移账单时
          if let categoryId = viewModel.selectedCategoryId {
            viewModel.prepareMigrateTransactions(from: categoryId, dataManager: dataManager)
          }
          viewModel.showingActionSheet = false
        },
        onDelete: {
          if let categoryId = viewModel.selectedCategoryId {
            viewModel.prepareDeleteCategory(categoryId, dataManager: dataManager)
          }
        },
        dismiss: { viewModel.showingActionSheet = false }
      )
    }
    .floatingSheet(
      isPresented: $viewModel.showingCategorySelector,
      config: SheetBase(
        maxDetent: .fraction(0.55),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      if let transaction = viewModel.tempTransaction,
        let sourceId = viewModel.migrationSourceCategoryId
      {
        SelectCategorySheet(
          title: "选择目标类别",
          transactionType: transaction.transactionType,
          selectedCategoryId: .constant(nil),
          categories: dataManager.mainCategories,
          subCategories: dataManager.subCategories,
          onCategorySelected: { targetCategoryId in
            // 执行迁移逻辑 - 从源类别迁移到目标类别
            viewModel.migrateTransactions(
              from: sourceId, to: targetCategoryId, using: modelContext, dataManager: dataManager)

            // 完成迁移后的清理
            viewModel.completeMigration()
          },
          onCancel: {
            viewModel.cancelMigration()
          }
        )
      }
    }

    // 成功迁移提示
    .overlay {
      if viewModel.showMigrationSuccess {
        VStack {
          Spacer()

          HStack(spacing: 8) {
            Image(systemName: "checkmark.circle.fill")
              .foregroundColor(.green)
            Text("已成功迁移 \(viewModel.migratedCount) 个交易")
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(.cBlack)
          }
          .padding(.horizontal, 20)
          .padding(.vertical, 12)
          .background(Color.white)
          .cornerRadius(20)
          .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
          .transition(.move(edge: .bottom).combined(with: .opacity))

          Spacer().frame(height: 100)
        }
      }
    }

    // 删除确认对话框（无交易）
    .alert("确认删除", isPresented: $viewModel.showDeleteConfirmation) {
      Button("取消", role: .cancel) {
        viewModel.cancelDelete()
      }
      Button("删除", role: .destructive) {
        if let categoryId = viewModel.categoryToDelete {
          viewModel.deleteCategory(categoryId, using: modelContext, dataManager: dataManager)
        }
      }
    } message: {
      if let categoryId = viewModel.categoryToDelete {
        if let category = dataManager.mainCategories.first(where: { $0.id == categoryId }) {
          Text("确定要删除「\(category.name)」吗？此操作无法撤销。")
        } else if let subCategory = dataManager.subCategories.first(where: { $0.id == categoryId })
        {
          Text("确定要删除「\(subCategory.name)」吗？此操作无法撤销。")
        }
      }
    }

    // 删除前迁移提示（有交易）
    .alert("该类别有关联交易", isPresented: $viewModel.showDeleteMigrationAlert) {
      Button("取消", role: .cancel) {
        viewModel.cancelDelete()
      }
      Button("迁移交易") {
        // 准备迁移
        if let categoryId = viewModel.categoryToDelete {
          viewModel.prepareMigrateTransactions(from: categoryId, dataManager: dataManager)
          viewModel.showDeleteMigrationAlert = false
        }
      }
      Button("直接删除", role: .destructive) {
        if let categoryId = viewModel.categoryToDelete {
          viewModel.deleteCategory(categoryId, using: modelContext, dataManager: dataManager)
        }
      }
    } message: {
      Text(
        "该类别下有 \(viewModel.deleteAffectedTransactionCount) 个交易记录。您可以先将这些交易迁移到其他类别，或直接删除类别（交易记录将失去分类）。"
      )
    }

    // 有子类别提示
    .alert("无法删除", isPresented: $viewModel.showHasSubCategoriesAlert) {
      Button("确定") {
        viewModel.cancelDelete()
      }
    } message: {
      Text("该类别下还有子类别，请先删除所有子类别后再删除主类别。")
    }
  }
}

// MARK: - 拖拽插入指示器

/// 拖拽插入位置指示器
struct DragInsertionIndicator: View {
  var body: some View {
    Rectangle()
      .fill(Color.cAccentBlue)
      .frame(height: 3)
      .cornerRadius(1.5)
      .padding(.horizontal, 16)
      .padding(.vertical, 4)
      .shadow(color: Color.cAccentBlue.opacity(0.3), radius: 2, x: 0, y: 1)
  }
}

// MARK: - 子类别展开视图

/// 子类别展开视图
///
/// 当主类别被选中时显示的子类别横向滚动列表，
/// 参考手动交易页面的设计风格，提供独立的背景和圆角设计。
struct SubCategoriesExpandedView: View {
  let subCategories: [TransactionSubCategoryModel]
  let parentCategory: TransactionMainCategoryModel
  let viewModel: TransactionCategoryVM
  let hapticManager: HapticFeedbackManager
  let pathManager: PathManagerHelper
  let modelContext: ModelContext
  let dataManager: DataManagement

  var body: some View {
    ScrollView(.horizontal, showsIndicators: false) {
      LazyHStack(spacing: 16) {
        ForEach(subCategories, id: \.id) { subCategory in
          SubCategoryItemView(
            subCategory: subCategory,
            isDragging: false,
            onTap: {
              hapticManager.trigger(.selection)
              viewModel.showSubCategoryActions(categoryId: subCategory.id)
            }
          )
        }

        AddSubCategoryButton(
          parentCategoryId: parentCategory.id,
          selectedType: viewModel.selectedType,
          hapticManager: hapticManager,
          pathManager: pathManager
        )
      }
      .padding(.horizontal, 16)
      .padding(.vertical, 12)
    }
    .background(Color.cWhite)
    .cornerRadius(16)
    .overlay(
      RoundedRectangle(cornerRadius: 16)
        .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
    )
    .padding(.top, 8)
  }
}

// MARK: - 子类别项视图

/// 单个子类别项视图
struct SubCategoryItemView: View {
  let subCategory: TransactionSubCategoryModel
  let isDragging: Bool
  let onTap: () -> Void

  var body: some View {
    VStack(spacing: 6) {
      IconView(
        viewModel: IconViewVM(
          icon: subCategory.icon,
          size: 44,
          fontSize: 22,
          backgroundColor: Color.cAccentBlue.opacity(0.05),
          cornerRadius: 12
        )
      )
      .overlay(
        RoundedRectangle(cornerRadius: 12)
          .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
      )
      .shadow(color: Color.cBlack.opacity(0.05), radius: 2, x: 0, y: 1)

      Text(subCategory.name)
        .font(.system(size: 12, weight: .regular))
        .foregroundColor(.cBlack.opacity(0.7))
        .lineLimit(1)
    }
    .frame(width: 60)
    .scaleEffect(isDragging ? 1.05 : 1.0)
    .opacity(isDragging ? 0.8 : 1.0)
    .contentShape(Rectangle())  // 确保整个区域可点击
    .onTapGesture {
      onTap()
    }
  }
}

// MARK: - 添加子类别按钮

/// 添加子类别按钮
struct AddSubCategoryButton: View {
  let parentCategoryId: String
  let selectedType: TransactionType
  let hapticManager: HapticFeedbackManager
  let pathManager: PathManagerHelper

  var body: some View {
    VStack(spacing: 6) {
      Button(action: {
        hapticManager.trigger(.impactLight)
        pathManager.path.append(
          NavigationDestination.createTransactionCategory(
            isMainCategory: false,
            mainCategoryId: parentCategoryId,
            selectedType: selectedType
          )
        )
      }) {
        Image(systemName: "plus.circle.fill")
          .font(.system(size: 22, weight: .medium))
          .foregroundColor(.cAccentBlue)
          .frame(width: 44, height: 44)
          .background(Color.cAccentBlue.opacity(0.05))
          .cornerRadius(12)
          .overlay(
            RoundedRectangle(cornerRadius: 12)
              .strokeBorder(Color.cAccentBlue.opacity(0.1), lineWidth: 1)
          )
      }

      Text("添加")
        .font(.system(size: 12, weight: .regular))
        .foregroundColor(.cBlack.opacity(0.7))
    }
    .frame(width: 60)
  }
}

// MARK: - 拖拽预览视图

/// 主类别拖拽预览视图
struct CategoryDragPreview: View {
  let category: TransactionMainCategoryModel

  var body: some View {
    VStack(spacing: 8) {
      IconView(
        viewModel: IconViewVM(
          icon: category.icon,
          size: 48,
          fontSize: 24,
          backgroundColor: Color.cAccentBlue.opacity(0.1),
          cornerRadius: 14
        ))

      Text(category.name)
        .font(.system(size: 14, weight: .medium))
        .foregroundColor(.cBlack)
        .lineLimit(1)
        .padding(.horizontal, 8)
    }
    .padding(16)
    .background(Color.cWhite)
    .cornerRadius(16)
    .shadow(color: Color.cBlack.opacity(0.15), radius: 8, x: 0, y: 4)
    .overlay(
      RoundedRectangle(cornerRadius: 16)
        .strokeBorder(Color.cAccentBlue.opacity(0.2), lineWidth: 1.5)
    )
  }
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct TransactionCategoryView_Previews: PreviewProvider {
    static var previews: some View {
      TransactionCategoryView()
    }
  }
#endif
