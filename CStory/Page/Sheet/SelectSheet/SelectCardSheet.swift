//
//  SelectCardSheet.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftData
import SwiftUI

/// 卡片选择弹窗
///
/// 显示用户的卡片列表，允许用户选择一张卡片。
/// 包含卡片列表、添加新卡片按钮和确认按钮。
///
/// ## 功能特性
/// - 显示所有已选中的卡片
/// - 支持添加新卡片
/// - 单选模式
/// - 触觉反馈
struct SelectCardSheet: View {
  // MARK: - 属性

  /// 弹窗标题
  let title: String
  /// 选中的卡片ID
  @Binding var selectedCardId: UUID?
  /// 卡片选择回调
  let onCardSelected: (UUID) -> Void
  /// 取消回调
  let onCancel: () -> Void

  // MARK: - 数据依赖

  /// 卡片列表
  let cards: [CardModel]

  // MARK: - 状态变量

  /// 临时选中的卡片ID
  @State private var tempSelectedCardId: UUID?
  /// 数据管理器
  @Environment(\.dataManager) private var dataManager

  // MARK: - 依赖注入

  /// 路由管理器
  @EnvironmentObject private var pathManager: PathManagerHelper

  // MARK: - 初始化
  init(
    title: String = "选择卡片",
    selectedCardId: Binding<UUID?>,
    cards: [CardModel],
    onCardSelected: @escaping (UUID) -> Void,
    onCancel: @escaping () -> Void
  ) {
    self.title = title
    self._selectedCardId = selectedCardId
    self.cards = cards
    self.onCardSelected = onCardSelected
    self.onCancel = onCancel
  }

  var body: some View {
    VStack(spacing: 12) {
      // 标题栏
      SheetTitle(
        title: title,
        button: "xmark.circle.fill",
        rightButtonAction: {
          dataManager.hapticManager.trigger(.impactLight)
          onCancel()
        }
      )

      // 卡片列表
      ScrollView {
        LazyVStack(spacing: 12) {
          // 显示所有已选中的卡片
          ForEach(cards.filter { $0.isSelected }) { card in
            CardRow(
              viewModel: CardRowVM(
                from: card,
                isSelected: tempSelectedCardId == card.id,
                showTypeTag: true,
                showAdditionalInfo: false,
                onTap: {
                  dataManager.hapticManager.trigger(.selection)
                  print("SelectCardSheet: 点击卡片 \(card.name), ID: \(card.id)")
                  print(
                    "SelectCardSheet: 更新前 tempSelectedCardId: \(tempSelectedCardId?.uuidString ?? "nil")"
                  )
                  tempSelectedCardId = card.id
                  print(
                    "SelectCardSheet: 更新后 tempSelectedCardId: \(tempSelectedCardId?.uuidString ?? "nil")"
                  )
                }
              ))
          }

          // 添加卡片按钮
          AddCardButton(
            viewModel: AddCardButtonVM.standard {
              pathManager.path.append(NavigationDestination.cardCategoryView)
            })
        }
        .padding(.horizontal, 16)
        .padding(.top, 1)
      }

      // 确认按钮
      Button(action: {
        dataManager.hapticManager.trigger(.impactMedium)
        if let cardId = tempSelectedCardId {
          onCardSelected(cardId)
        }
      }) {
        Text("确定")
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(.white)
          .frame(maxWidth: .infinity)
          .frame(height: 48)
          .background(
            tempSelectedCardId != nil
              ? Color.cAccentBlue : Color.cAccentBlue.opacity(0.3)
          )
          .cornerRadius(24)
      }
      .disabled(tempSelectedCardId == nil)
      .padding(.horizontal, 16)
      .padding(.bottom, 12)
    }
    .onAppear {
      // 初始化临时选中状态
      print("SelectCardSheet: onAppear - 初始化 selectedCardId: \(selectedCardId?.uuidString ?? "nil")")
      tempSelectedCardId = selectedCardId
      print("SelectCardSheet: onAppear - 设置 tempSelectedCardId: \(tempSelectedCardId?.uuidString ?? "nil")")
    }
  }

}

#Preview {
  SelectCardSheet(
    title: "选择卡片",
    selectedCardId: .constant(nil),
    cards: [],
    onCardSelected: { _ in },
    onCancel: {}
  )
}
