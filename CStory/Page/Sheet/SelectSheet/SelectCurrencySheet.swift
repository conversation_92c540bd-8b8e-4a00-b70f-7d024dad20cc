//
//  SelectCurrencySheet.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftData
import SwiftUI

// MARK: - 货币选择 Sheet

/// 货币选择弹窗
///
/// 支持多种显示模式，用于不同场景的货币选择：
/// - all: 显示所有已选中的货币
/// - baseAndCard: 只显示本位币和卡片货币
/// - simpleConversion: 仅显示基础货币和备选货币
/// - baseCurrencySelection: 本位币选择模式
///
/// ## 功能特性
/// - 根据模式过滤货币列表
/// - 本位币优先显示
/// - 支持回调通知选择变化
struct SelectCurrencySheet: View {
  // MARK: - 环境属性

  @Environment(\.dismiss) private var dismiss
  @Environment(\.modelContext) private var modelContext
  @Environment(\.dataManager) private var dataManager

  // MARK: - 绑定属性

  /// 当前选中的货币代码
  @Binding var selectedCurrencyCode: String
  /// 货币符号，支持绑定但有默认值
  @Binding var currencySymbol: String

  // MARK: - 属性

  /// sheet 显示模式：all-所有货币, baseAndCard-只显示本位币和卡片货币, simpleConversion-仅显示基础货币和备选货币, baseCurrencySelection-本位币选择模式
  var mode: String = "all"

  /// 选择完成的回调
  var onSelect: ((String) -> Void)?

  /// 本位币选择完成的回调（用于本位币选择模式）
  var onBaseCurrencySelect: ((CurrencyModel) -> Void)?

  /// 简单转换模式参数
  var baseCurrencyCode: String?
  var alternativeCurrencyCode: String?

  // MARK: - 计算属性

  /// 获取本位币代码
  private var defaultBaseCurrencyCode: String {
    baseCurrencyCode ?? UserDefaults.standard.string(forKey: "baseCurrencyCode") ?? "CNY"
  }

  /// 获取本位币对象
  private var baseCurrency: CurrencyModel? {
    dataManager.currencies.first { $0.code == defaultBaseCurrencyCode }
  }

  /// 获取卡片中使用的货币列表
  private var cardCurrencies: [CurrencyModel] {
    // 从所有卡片中提取货币代码
    let cardCurrencyCodes = Set(dataManager.cards.map { $0.currency })
    // 根据这些代码查找对应的货币对象
    return dataManager.currencies.filter { currency in
      cardCurrencyCodes.contains(currency.code)
    }
  }

  /// 根据模式确定显示的货币列表
  private var currencies: [CurrencyModel] {
    // 本位币选择模式 - 显示所有货币供选择本位币
    if mode == "baseCurrencySelection" {
      return dataManager.currencies.sorted(by: { $0.order < $1.order })
    }
    // 简单转换模式 - 只显示两个货币选项
    else if mode == "simpleConversion" {
      var result: [CurrencyModel] = []

      // 添加本位币
      if let base = dataManager.currencies.first(where: { $0.code == defaultBaseCurrencyCode }) {
        result.append(base)
      }

      // 添加备选货币（如果与本位币不同）
      if let altCode = alternativeCurrencyCode, altCode != defaultBaseCurrencyCode,
        let altCurrency = dataManager.currencies.first(where: { $0.code == altCode })
      {
        result.append(altCurrency)
      }

      return result

    } else if mode == "baseAndCard" {
      // 基础货币和卡片货币模式
      // 合并本位币和卡片货币，并去重
      var currencySet = Set<String>()
      var result: [CurrencyModel] = []

      // 先添加本位币（如果存在）
      if let base = baseCurrency {
        result.append(base)
        currencySet.insert(base.code)
      }

      // 然后添加卡片货币（去重）
      for currency in cardCurrencies {
        if !currencySet.contains(currency.code) {
          result.append(currency)
          currencySet.insert(currency.code)
        }
      }

      return result.sorted(by: { $0.order < $1.order })
    } else {
      // "all" 模式 - 显示本位币、当前选中的货币和isSelected=true的货币
      var result: [CurrencyModel] = []
      var currencySet = Set<String>()

      // 1. 首先添加本位币（如果存在）
      if let base = baseCurrency {
        result.append(base)
        currencySet.insert(base.code)
      }

      // 2. 添加当前选中的货币（如果不是本位币）
      if !selectedCurrencyCode.isEmpty {
        let selectedCurrency = dataManager.currencies.first { $0.code == selectedCurrencyCode }
        if let selected = selectedCurrency, !currencySet.contains(selected.code) {
          result.append(selected)
          currencySet.insert(selected.code)
        }
      }

      // 3. 添加其他标记为isSelected=true的货币
      for currency in dataManager.currencies {
        if currency.isSelected && !currencySet.contains(currency.code) {
          result.append(currency)
          currencySet.insert(currency.code)
        }
      }

      return result.sorted(by: { $0.order < $1.order })
    }
  }

  var body: some View {
    VStack(spacing: 12) {
      // 标题栏
      SheetTitle(
        title: "选择货币", button: "xmark.circle.fill",
        rightButtonAction: {
          dataManager.hapticManager.trigger(.impactLight)
          dismiss()
        })

      // 货币列表
      ScrollView {
        LazyVStack(spacing: 12) {
          ForEach(currencies) { currency in
            HStack(spacing: 12) {
              // 货币图标
              Image(systemName: "dollarsign.circle")
                .font(.system(size: 20))
                .foregroundColor(
                  (mode == "baseCurrencySelection"
                    ? isBaseCurrency(currency) : currency.code == selectedCurrencyCode)
                    ? .white : Color.cAccentBlue
                )
                .frame(width: 44, height: 44)
                .background(
                  (mode == "baseCurrencySelection"
                    ? isBaseCurrency(currency) : currency.code == selectedCurrencyCode)
                    ? Color.cAccentBlue : Color.cAccentBlue.opacity(0.1)
                )
                .cornerRadius(12)

              // 货币信息
              VStack(alignment: .leading, spacing: 2) {
                HStack(spacing: 8) {
                  if mode == "simpleConversion" {
                    // 简化模式下只显示名称
                    Text(isBaseCurrency(currency) ? "本位币" : "卡片货币")
                      .font(.system(size: 14, weight: .medium))
                      .foregroundColor(
                        (mode == "baseCurrencySelection"
                          ? isBaseCurrency(currency) : currency.code == selectedCurrencyCode)
                          ? Color.cAccentBlue : Color.cBlack
                      )
                  } else {
                    Text(currency.name)
                      .font(.system(size: 14, weight: .medium))
                      .foregroundColor(
                        (mode == "baseCurrencySelection"
                          ? isBaseCurrency(currency) : currency.code == selectedCurrencyCode)
                          ? Color.cAccentBlue : Color.cBlack
                      )
                  }

                  // 本位币选择模式下显示"当前"标识
                  if mode == "baseCurrencySelection" && isBaseCurrency(currency) {
                    Text("当前")
                      .font(.system(size: 12, weight: .medium))
                      .foregroundColor(.cWhite)
                      .padding(.horizontal, 8)
                      .padding(.vertical, 2)
                      .background(Color.cAccentBlue)
                      .cornerRadius(8)
                  }
                }

                Text("\(currency.code) · \(currency.symbol)")
                  .font(.system(size: 12))
                  .foregroundColor(.cBlack.opacity(0.6))
              }

              Spacer()

              // 选中状态标记
              if mode == "baseCurrencySelection"
                ? isBaseCurrency(currency) : currency.code == selectedCurrencyCode
              {
                Image(systemName: "checkmark")
                  .font(.system(size: 14, weight: .semibold))
                  .foregroundColor(.cAccentBlue)
              }
            }
            .padding(4)
            .background(Color.cWhite)
            .cornerRadius(16)
            .overlay(
              RoundedRectangle(cornerRadius: 16)
                .strokeBorder(
                  (mode == "baseCurrencySelection"
                    ? isBaseCurrency(currency) : currency.code == selectedCurrencyCode)
                    ? Color.cAccentBlue : Color.cAccentBlue.opacity(0.08),
                  lineWidth: (mode == "baseCurrencySelection"
                    ? isBaseCurrency(currency) : currency.code == selectedCurrencyCode) ? 1.5 : 1
                )
            )
            .onTapGesture {
              dataManager.hapticManager.trigger(.selection)
              if mode == "baseCurrencySelection" {
                // 本位币选择模式
                if !isBaseCurrency(currency) {
                  onBaseCurrencySelect?(currency)
                  dismiss()
                }
              } else {
                // 其他模式
                let oldValue = selectedCurrencyCode
                selectedCurrencyCode = currency.code
                currencySymbol = currency.symbol
                // 只有当货币代码真正变化时才调用回调
                if oldValue != currency.code {
                  onSelect?(currency.code)
                }
                dismiss()
              }
            }
          }
        }
        .padding(.horizontal, 16)
        .padding(.top, 1)
      }
      Spacer()
    }

  }
  // MARK: - 私有方法

  /// 判断是否为本位币
  private func isBaseCurrency(_ currency: CurrencyModel) -> Bool {
    currency.code == defaultBaseCurrencyCode
  }
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct SelectCurrencySheet_Previews: PreviewProvider {
    static var previews: some View {
      PreviewContainer()
    }
  }

  struct PreviewContainer: View {
    @State private var showSheet = false
    @State private var selectedCurrencyCode = "CNY"
    @State private var currencySymbol = "¥"

    var body: some View {
      VStack {
        Button("选择货币") {
          showSheet = true
        }
        .padding()
        .background(Color.cAccentBlue)
        .foregroundColor(.white)
        .cornerRadius(12)

        Text("当前货币: \(selectedCurrencyCode) (\(currencySymbol))")
          .padding()
      }
      .frame(maxWidth: .infinity, maxHeight: .infinity)
      .background(Color.cLightBlue)
      .floatingSheet(
        isPresented: $showSheet,
        config: SheetBase(
          maxDetent: .fraction(0.7),
          cornerRadius: 24,
          interactiveDimiss: true,
          hPadding: 8,
          bPadding: 4
        )
      ) {
        SelectCurrencySheet(
          selectedCurrencyCode: $selectedCurrencyCode,
          currencySymbol: $currencySymbol,
          mode: "all",
          onSelect: { code in
            selectedCurrencyCode = code
            showSheet = false
          }
        )
        .environment(\.dataManager, createPreviewDataManager())
      }
    }

    /// 创建预览用的DataManagement
    static func createPreviewDataManager() -> DataManagement {
      let currencies = [
        CurrencyModel(
          name: "人民币",
          code: "CNY",
          symbol: "¥",
          rate: 1.0,
          isBaseCurrency: true,
          order: 0
        ),
        CurrencyModel(
          name: "美元",
          code: "USD",
          symbol: "$",
          rate: 7.2,
          isBaseCurrency: false,
          order: 1
        ),
        CurrencyModel(
          name: "欧元",
          code: "EUR",
          symbol: "€",
          rate: 7.8,
          isBaseCurrency: false,
          order: 2
        ),
      ]

      return DataManagement(
        cards: [],
        mainCategories: [],
        subCategories: [],
        currencies: currencies,
        recentTransactions: [],
        allTransactions: []
      )
    }
  }
#endif
