//
//  BackgroundOptionsSheet.swift
//  CStory
//
//  Created by NZUE on 2025/12/19.
//

import SwiftUI

/// 卡片背景选择 Sheet
struct SelectCoverSheet: View {
  @Binding var selectedType: CardCoverType
  @Binding var isDarkBackground: Bool
  @Environment(\.dismiss) private var dismiss

  // MARK: - 状态参数
  @State private var selectedPreset: CardCoverType?

  // 使用背景管理器获取预设背景
  private var presets: [CardCoverHelper.PresetCoverConfig] {
    return CardCoverHelper.presetCovers
  }

  // 计算屏幕宽度
  private var screenWidth: CGFloat {
    UIScreen.main.bounds.width
  }

  // 计算卡片宽度 - 每行3个
  private var cardWidth: CGFloat {
    (screenWidth - 80) / 3  // 考虑左右边距和间距
  }

  // MARK: - Body

  var body: some View {
    VStack(spacing: 0) {
      // 标题栏
      SheetTitle(
        title: "选择背景样式", button: "xmark.circle.fill",
        rightButtonAction: {
          dismiss()
        })

      // 主内容区
      ScrollView {
        VStack(alignment: .leading, spacing: 20) {
          // 预设背景选择区域
          VStack(alignment: .leading, spacing: 12) {
            Text("预设背景")
              .font(.system(size: 16, weight: .medium))
              .foregroundColor(.cBlack)
              .padding(.horizontal, 16)

            // 使用LazyVGrid显示预设背景
            LazyVGrid(
              columns: [
                GridItem(.flexible(), spacing: 12),
                GridItem(.flexible(), spacing: 12),
                GridItem(.flexible(), spacing: 12),
              ], spacing: 20
            ) {
              ForEach(0..<presets.count, id: \.self) { index in
                let preset = presets[index]
                CardPreviewItem(
                  title: preset.title,
                  imageName: preset.imageName,
                  isSelected: selectedType == preset.type,
                  width: cardWidth
                )
                .onTapGesture {
                  selectedType = preset.type
                  isDarkBackground = preset.isDark
                }
              }
            }
            .padding(.horizontal, 16)
          }
        }
        .padding(.vertical, 20)
      }
    }

    .edgesIgnoringSafeArea(.bottom)
  }
}

// MARK: - 卡片预览项

/// 卡片背景预览项
private struct CardPreviewItem: View {
  var title: String
  var imageName: String
  var isSelected: Bool
  var width: CGFloat

  var body: some View {
    VStack(spacing: 8) {
      // 图片
      Image(imageName)
        .resizable()
        .aspectRatio(contentMode: .fill)
        .frame(width: width, height: width * 0.6)
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .overlay(
          RoundedRectangle(cornerRadius: 12)
            .strokeBorder(Color.cAccentBlue, lineWidth: isSelected ? 2 : 0)
        )
        .overlay(
          // 勾选标记
          isSelected
            ? Image(systemName: "checkmark.circle.fill")
              .foregroundColor(.cAccentBlue)
              .background(Circle().fill(.white))
              .font(.system(size: 18, weight: .bold))
              .padding(8)
              .transition(.opacity) : nil,
          alignment: .topTrailing
        )

      // 标题
      Text(title)
        .font(.system(size: 12, weight: .medium))
        .foregroundColor(.cBlack)
        .frame(maxWidth: .infinity, alignment: .center)
    }
    .padding(4)
    .background(isSelected ? Color.cAccentBlue.opacity(0.05) : Color.clear)
    .cornerRadius(12)
    .animation(.easeInOut(duration: 0.2), value: isSelected)
  }
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct SelectCoverSheet_Previews: PreviewProvider {
    static var previews: some View {
      Group {
        // 默认背景选择预览
        SelectCoverSheet(
          selectedType: .constant(.card1),
          isDarkBackground: .constant(false)
        )
        .previewDisplayName("默认背景选择")

        // 深色背景选择预览
        SelectCoverSheet(
          selectedType: .constant(.card2),
          isDarkBackground: .constant(true)
        )
        .previewDisplayName("深色背景选择")
      }
      .frame(height: 500)
      .background(Color.cLightBlue)
    }
  }
#endif
