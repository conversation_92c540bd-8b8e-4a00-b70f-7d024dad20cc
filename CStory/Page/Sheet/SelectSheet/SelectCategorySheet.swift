//
//  SelectCategorySheet.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftData
import SwiftUI

/// 交易分类选择弹窗
///
/// 显示交易分类网格，允许用户选择主分类或子分类。
/// 支持点击主分类展开/收起子分类列表。
///
/// ## 功能特性
/// - 根据交易类型过滤分类
/// - 主分类网格展示
/// - 子分类可折叠列表
/// - 支持初始选中状态
struct SelectCategorySheet: View {
  // MARK: - 属性
  
  /// 弹窗标题
  let title: String
  /// 交易类型
  let transactionType: TransactionType
  /// 选中的分类ID
  @Binding var selectedCategoryId: String?
  /// 分类选择回调
  let onCategorySelected: (String) -> Void
  /// 取消回调
  let onCancel: () -> Void

  // MARK: - 数据依赖
  
  /// 主分类列表
  let categories: [TransactionMainCategoryModel]
  /// 子分类列表
  let subCategories: [TransactionSubCategoryModel]

  // MARK: - 状态变量
  
  /// 选中的主分类
  @State private var selectedMainCategory: TransactionMainCategoryModel?
  /// 选中的子分类
  @State private var selectedSubCategory: TransactionSubCategoryModel?
  /// 是否显示子分类
  @State private var showSubCategories: Bool = false
  /// 数据管理器
  @Environment(\.dataManager) private var dataManager

  // MARK: - 初始化
  init(
    title: String = "选择类别",
    transactionType: TransactionType,
    selectedCategoryId: Binding<String?>,
    categories: [TransactionMainCategoryModel],
    subCategories: [TransactionSubCategoryModel],
    onCategorySelected: @escaping (String) -> Void,
    onCancel: @escaping () -> Void
  ) {
    self.title = title
    self.transactionType = transactionType
    self._selectedCategoryId = selectedCategoryId
    self.categories = categories
    self.subCategories = subCategories
    self.onCategorySelected = onCategorySelected
    self.onCancel = onCancel
  }

  // MARK: - 计算属性
  
  /// 根据交易类型过滤的分类
  private var filteredCategories: [TransactionMainCategoryModel] {
    categories.filter { $0.type == transactionType.rawValue }.sorted { $0.order < $1.order }
  }

  var body: some View {
    VStack(spacing: 12) {
      // 标题栏
      SheetTitle(
        title: title, 
        button: "xmark.circle.fill",
        rightButtonAction: {
            dataManager.hapticManager.trigger(.impactLight)
            onCancel()
        }
      )

      // 类别网格
      ScrollView {
        LazyVStack(alignment: .leading, spacing: 12) {
          // 将分类按每行5个划分
          let rows = filteredCategories.chunked(into: 5)
          ForEach(Array(rows.enumerated()), id: \.offset) { rowIndex, row in
            CategoryRow(
              row: row,
              selectedId: selectedMainCategory?.id,
              selectedMainCategory: selectedMainCategory,
              selectedSubId: selectedSubCategory?.id,
              showSubCategories: showSubCategories,
              onSelect: { category in
                dataManager.hapticManager.trigger(.selection)
                if selectedMainCategory?.id == category.id {
                  // 点击已选中的主分类，切换子分类显示
                  showSubCategories.toggle()
                  selectedSubCategory = nil
                } else {
                  // 选择新的主分类
                  selectedMainCategory = category
                  selectedSubCategory = nil
                  showSubCategories = true
                }
              },
              onSelectSub: { subCategory in
                dataManager.hapticManager.trigger(.selection)
                selectedSubCategory = subCategory
                selectedMainCategory = categories.first(where: { $0.id == subCategory.mainId })
              },
              onSelectMain: { category in
                dataManager.hapticManager.trigger(.selection)
                // 直接选择没有子分类的主分类
                selectedMainCategory = category
                selectedSubCategory = nil
                showSubCategories = false
              }
            )
          }
        }
        .padding(.horizontal, 12)
      }

      // 确认按钮
      Button(action: {
        dataManager.hapticManager.trigger(.impactMedium)
        // 优先使用子分类ID，如果没有则使用主分类ID
        let categoryId = selectedSubCategory?.id ?? selectedMainCategory?.id
        if let categoryId = categoryId {
          onCategorySelected(categoryId)
        }
      }) {
        Text("确定")
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(.white)
          .frame(maxWidth: .infinity)
          .frame(height: 48)
          .background(
            (selectedMainCategory != nil || selectedSubCategory != nil)
              ? Color.cAccentBlue : Color.cAccentBlue.opacity(0.3)
          )
          .cornerRadius(24)
      }
      .disabled(selectedMainCategory == nil && selectedSubCategory == nil)
      .padding(.horizontal, 16)
      .padding(.bottom, 12)
    }
    .onAppear {
      setupInitialState()
    }
  }

  // MARK: - 私有方法
  
  /// 设置初始状态
  private func setupInitialState() {
    if let categoryId = selectedCategoryId {
      // 先查找子分类
      if let subCategory = subCategories.first(where: { $0.id == categoryId }) {
        selectedSubCategory = subCategory
        selectedMainCategory = categories.first(where: { $0.id == subCategory.mainId })
        showSubCategories = true
      } else if let mainCategory = categories.first(where: { $0.id == categoryId }) {
        // 再查找主分类
        selectedMainCategory = mainCategory
      }
    }
  }
}

// MARK: - 分类行视图
/// 分类行视图
///
/// 显示一行主分类图标，并在下方显示对应的子分类列表。
private struct CategoryRow: View {
  let row: [TransactionMainCategoryModel]
  let selectedId: String?
  let selectedMainCategory: TransactionMainCategoryModel?
  let selectedSubId: String?
  let showSubCategories: Bool
  let onSelect: (TransactionMainCategoryModel) -> Void
  let onSelectSub: (TransactionSubCategoryModel) -> Void
  let onSelectMain: (TransactionMainCategoryModel) -> Void

  var body: some View {
    VStack(spacing: 12) {
      // 主分类行
      HStack(spacing: 12) {
        ForEach(row) { category in
          CategoryItemView(
            icon: category.icon,
            name: category.name,
            isSelected: category.id == selectedId
          )
          .onTapGesture {
            if category.subCategories?.isEmpty ?? true {
              // 如果没有子类别，直接选择主类别
              onSelectMain(category)
            } else {
              // 如果有子类别，展开/收起子类别
              onSelect(category)
            }
          }
        }
        // 补齐空位
        if row.count < 5 {
          ForEach(0..<(5 - row.count), id: \.self) { _ in
            Spacer().frame(maxWidth: .infinity)
          }
        }
      }
      .padding(.horizontal, 12)

      // 子分类列表
      if shouldShowSubCategories(for: row) {
        SubCategoriesListView(
          subCategories: selectedMainCategory?.subCategories ?? [],
          selectedId: selectedSubId,
          onSelect: onSelectSub
        )
      }
    }
  }

  /// 判断是否显示子分类
  private func shouldShowSubCategories(for row: [TransactionMainCategoryModel]) -> Bool {
    showSubCategories && selectedMainCategory != nil
      && row.contains(where: { $0.id == selectedMainCategory?.id })
      && !(selectedMainCategory?.subCategories?.isEmpty ?? true)
  }
}

#Preview {
  SelectCategorySheet(
    title: "选择类别",
    transactionType: .expense,
    selectedCategoryId: .constant(nil),
    categories: [],
    subCategories: [],
    onCategorySelected: { _ in },
    onCancel: {}
  )
}
