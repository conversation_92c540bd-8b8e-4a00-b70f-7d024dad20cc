//
//  CurrencyRateSheet.swift
//  CStory
//
//  Created by NZUE on 2025/8/1.
//

import SwiftData
import SwiftUI

/// 货币汇率设置Sheet
///
/// 用于设置单个货币的自定义汇率，支持：
/// - 查看当前汇率
/// - 输入自定义汇率
/// - 恢复默认汇率
/// - 汇率有效性验证
struct CurrencyRateSheet: View {
  
  // MARK: - Environment Properties
  
  @Environment(\.dismiss) private var dismiss
  @Environment(\.modelContext) private var modelContext
  @Environment(\.dataManager) private var dataManager
  
  // MARK: - Properties
  
  /// 货币数据
  let currency: CurrencyModel
  
  /// ViewModel
  @ObservedObject private var viewModel = CurrencyRateSheetVM()
  
  // MARK: - Body
  
  var body: some View {
    VStack(spacing: 16) {
      // 货币信息头部
      headerSection
      
      // 汇率设置区域
      rateSettingSection
      
      Spacer()
    }
    .padding(.top, 16)
    .onAppear {
      viewModel.configure(with: currency)
    }
    .alert("恢复默认汇率", isPresented: $viewModel.showRestoreAlert) {
      Button("取消", role: .cancel) {}
      Button("确认恢复", role: .destructive) {
        viewModel.restoreDefaultRate(using: modelContext) {
          dismiss()
        }
      }
    } message: {
      Text("确定要恢复默认汇率吗？这将清除您的自定义设置。")
    }
  }
  
  // MARK: - View Components
  
  /// 货币信息头部
  private var headerSection: some View {
    HStack {
      VStack(alignment: .leading, spacing: 4) {
        Text(currency.name)
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(.cBlack)
        
        Text("\(currency.code) · \(currency.symbol)")
          .font(.system(size: 14, weight: .regular))
          .foregroundColor(.cBlack.opacity(0.6))
      }
      
      Spacer()
      
      // 关闭按钮
      Button(action: {
        dataManager.hapticManager.trigger(.impactLight)
        dismiss()
      }) {
        Image(systemName: "xmark")
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(.cBlack.opacity(0.6))
          .frame(width: 32, height: 32)
          .background(Color.cWhite)
          .cornerRadius(16)
          .overlay(
            RoundedRectangle(cornerRadius: 16)
              .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
          )
      }
    }
    .padding(.horizontal, 16)
  }
  
  /// 汇率设置区域
  private var rateSettingSection: some View {
    VStack(spacing: 12) {
      // 当前汇率显示
      currentRateRow
      
      // 自定义汇率输入
      customRateInputRow
      
      // 操作按钮
      actionButtonsRow
    }
    .padding(.horizontal, 16)
    .padding(.vertical, 12)
    .background(Color.cWhite)
    .cornerRadius(16)
    .overlay(
      RoundedRectangle(cornerRadius: 16)
        .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
    )
    .padding(.horizontal, 16)
  }
  
  /// 当前汇率行
  private var currentRateRow: some View {
    HStack {
      Text("当前汇率")
        .font(.system(size: 14, weight: .medium))
        .foregroundColor(.cBlack)
      
      Spacer()
      
      Text(viewModel.currentRateText)
        .font(.system(size: 14, weight: .regular))
        .foregroundColor(.cBlack.opacity(0.6))
    }
  }
  
  /// 自定义汇率输入行
  private var customRateInputRow: some View {
    HStack {
      Text("自定义汇率")
        .font(.system(size: 14, weight: .medium))
        .foregroundColor(.cBlack)
      
      Spacer()
      
      TextField("", text: $viewModel.customRateText)
        .font(.system(size: 14, weight: .regular))
        .foregroundColor(.cAccentBlue)
        .multilineTextAlignment(.trailing)
        .keyboardType(.decimalPad)
        .frame(width: 100)
        .onTapGesture {
          viewModel.handleRateInputTap()
        }
    }
  }
  
  /// 操作按钮行
  private var actionButtonsRow: some View {
    HStack(spacing: 12) {
      // 恢复默认按钮
      Button(action: {
        dataManager.hapticManager.trigger(.warning)
        viewModel.showRestoreConfirmation()
      }) {
        Text("恢复默认")
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.cBlack.opacity(0.6))
          .padding(.horizontal, 16)
          .padding(.vertical, 8)
          .background(Color.cWhite)
          .cornerRadius(16)
          .overlay(
            RoundedRectangle(cornerRadius: 16)
              .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
          )
      }
      .disabled(!viewModel.canRestoreDefault)
      .opacity(viewModel.canRestoreDefault ? 1 : 0.5)
      
      // 确认按钮
      Button(action: {
        dataManager.hapticManager.trigger(.impactMedium)
        viewModel.saveCustomRate(using: modelContext) {
          dismiss()
        }
      }) {
        Text("确认")
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.cWhite)
          .padding(.horizontal, 16)
          .padding(.vertical, 8)
          .background(Color.cAccentBlue)
          .cornerRadius(16)
      }
      .disabled(!viewModel.canSaveCustomRate)
      .opacity(viewModel.canSaveCustomRate ? 1 : 0.5)
    }
  }
}

#if DEBUG
struct CurrencyRateSheet_Previews: PreviewProvider {
  static var previews: some View {
    CurrencyRatePreviewContainer()
  }
}

struct CurrencyRatePreviewContainer: View {
  @State private var showSheet = false
  @State private var selectedCurrency = createSampleCurrency(isBaseCurrency: false, isCustom: false)

  var body: some View {
    VStack(spacing: 16) {
      Button("普通货币汇率设置") {
        selectedCurrency = Self.createSampleCurrency(isBaseCurrency: false, isCustom: false)
        showSheet = true
      }
      .padding()
      .background(Color.cAccentBlue)
      .foregroundColor(.white)
      .cornerRadius(12)

      Button("自定义汇率货币") {
        selectedCurrency = Self.createSampleCurrency(isBaseCurrency: false, isCustom: true)
        showSheet = true
      }
      .padding()
      .background(Color.green)
      .foregroundColor(.white)
      .cornerRadius(12)

      Button("本位币（禁用状态）") {
        selectedCurrency = Self.createSampleCurrency(isBaseCurrency: true, isCustom: false)
        showSheet = true
      }
      .padding()
      .background(Color.orange)
      .foregroundColor(.white)
      .cornerRadius(12)
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
    .background(Color.cLightBlue)
    .floatingSheet(
      isPresented: $showSheet,
      config: SheetBase(
        maxDetent: .fraction(0.4),
        cornerRadius: 24,
        interactiveDimiss: true,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      CurrencyRateSheet(currency: selectedCurrency)
        .environment(\.dataManager, Self.createBasicDataManager())
    }
  }
}
  
  /// 创建示例货币
  static func createSampleCurrency(isBaseCurrency: Bool, isCustom: Bool) -> CurrencyModel {
    return CurrencyModel(
      name: isBaseCurrency ? "人民币" : "美元",
      code: isBaseCurrency ? "CNY" : "USD",
      symbol: isBaseCurrency ? "¥" : "$",
      rate: isBaseCurrency ? 1.0 : 0.138,
      customRate: isCustom ? 0.140 : nil,
      isBaseCurrency: isBaseCurrency,
      isCustom: isCustom,
      order: 0
    )
  }
  
  /// 创建基础数据管理器
  static func createBasicDataManager() -> DataManagement {
    return DataManagement(
      cards: [],
      mainCategories: [],
      subCategories: [],
      currencies: [],
      recentTransactions: [],
      allTransactions: [],
      chatMessages: []
    )
  }
}
#endif