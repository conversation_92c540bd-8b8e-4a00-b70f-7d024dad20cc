//
//  CategoryActionSheet.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

/// 交易分类操作弹窗
///
/// 提供交易分类相关的操作选项，包括编辑分类信息、排序类别、迁移账单到其他分类、删除分类等功能。
/// 使用统一的操作选项卡片样式，支持危险操作的视觉区分。
struct CategoryActionSheet: View {

  // MARK: - 属性

  /// 编辑分类回调
  let onEdit: () -> Void

  /// 排序类别回调
  let onSort: () -> Void

  /// 迁移账单回调
  let onMigrate: () -> Void

  /// 删除分类回调
  let onDelete: () -> Void

  /// 关闭弹窗回调
  let dismiss: () -> Void

  // MARK: - 状态

  /// 数据管理器
  @Environment(\.dataManager) private var dataManager

  // MARK: - 计算属性

  /// 操作选项配置
  private var actionOptions:
    [(title: String, icon: String, isDestructive: Bool, action: () -> Void)]
  {
    [
      (
        "编辑类别", "pencil", false,
        {
          dataManager.hapticManager.trigger(.impactLight)
          onEdit()
          dismiss()
        }
      ),
      (
        "类别排序", "arrow.up.arrow.down", false,
        {
          dataManager.hapticManager.trigger(.impactLight)
          onSort()
          dismiss()
        }
      ),
      (
        "迁移账单", "arrow.triangle.2.circlepath", false,
        {
          dataManager.hapticManager.trigger(.impactMedium)
          onMigrate()
          dismiss()
        }
      ),
      (
        "删除类别", "trash", true,
        {
          dataManager.hapticManager.trigger(.warning)
          onDelete()
          dismiss()
        }
      ),
    ]
  }

  // MARK: - 主体

  var body: some View {
    VStack(spacing: 12) {
      // MARK: 标题栏
      HStack {
        Text("类别操作")
          .font(.system(size: 18, weight: .medium))
          .foregroundColor(.cBlack)

        Spacer()

        // 关闭按钮
        Button(action: {
          dataManager.hapticManager.trigger(.impactLight)
          dismiss()
        }) {
          Image(systemName: "xmark.circle.fill")
            .foregroundColor(.cBlack.opacity(0.6))
            .font(.system(size: 18, weight: .medium))
        }
      }
      .padding(.horizontal, 16)
      .padding(.top, 12)

      // MARK: 操作选项列表
      ForEach(Array(actionOptions.enumerated()), id: \.offset) { index, option in
        HStack(spacing: 12) {
          // 操作图标
          Image(systemName: option.icon)
            .font(.system(size: 20))
            .foregroundColor(option.isDestructive ? .white : Color.cAccentBlue)
            .frame(width: 44, height: 44)
            .background(
              option.isDestructive ? Color.cAccentRed : Color.cAccentBlue.opacity(0.1)
            )
            .cornerRadius(12)

          // 操作标题
          Text(option.title)
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(option.isDestructive ? Color.cAccentRed : Color.cBlack)

          Spacer()
        }
        .padding(4)
        .background(Color.cWhite)
        .cornerRadius(16)
        .overlay(
          RoundedRectangle(cornerRadius: 16)
            .strokeBorder(
              option.isDestructive
                ? Color.cAccentRed.opacity(0.2) : Color.cAccentBlue.opacity(0.08),
              lineWidth: 1
            )
        )
        .padding(.horizontal, 16)
        .onTapGesture {
          option.action()
        }
      }

      Spacer()
    }

  }
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct CategoryActionSheet_Previews: PreviewProvider {
    static var previews: some View {
      Group {
        // 主类别操作预览
        CategoryActionSheet(
          onEdit: { print("编辑主类别") },
          onSort: { print("排序主类别") },
          onMigrate: { print("迁移主类别账单") },
          onDelete: { print("删除主类别") },
          dismiss: { print("关闭弹窗") }
        )
        .environment(\.dataManager, DataManagement())
        .previewDisplayName("主类别操作")

        // 子类别操作预览
        CategoryActionSheet(
          onEdit: { print("编辑子类别") },
          onSort: { print("排序子类别") },
          onMigrate: { print("迁移子类别账单") },
          onDelete: { print("删除子类别") },
          dismiss: { print("关闭弹窗") }
        )
        .environment(\.dataManager, DataManagement())
        .previewDisplayName("子类别操作")
      }
      .frame(height: 300)
      .background(Color.cLightBlue)
    }
  }
#endif
