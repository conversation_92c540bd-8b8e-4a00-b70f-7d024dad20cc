//
//  CardFilterSheet.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

/// 卡片筛选弹窗
///
/// 提供卡片类型筛选功能，支持显示所有卡片、仅储蓄卡或仅信用卡。
/// 使用统一的选项卡片样式，带有选中状态指示。
struct CardFilterSheet: View {

  // MARK: - 属性

  /// 当前选中的筛选条件
  /// - nil: 显示所有卡片
  /// - false: 仅显示储蓄卡
  /// - true: 仅显示信用卡
  @Binding var selectedFilter: Bool?

  /// 关闭弹窗回调
  var dismiss: () -> Void

  /// 筛选条件改变回调
  var onFilterChanged: ((Bool?) -> Void)? = nil

  // MARK: - 常量

  /// 筛选选项配置
  private let filterOptions: [(title: String, filter: Bool?, icon: String)] = [
    ("所有卡片", nil, "creditcard.fill"),
    ("储蓄卡", false, "banknote.fill"),
    ("信用卡", true, "creditcard"),
  ]

  // MARK: - 主体

  var body: some View {
    VStack(spacing: 12) {
      // MARK: 标题栏
      HStack {
        Text("筛选卡片")
          .font(.system(size: 18, weight: .medium))
          .foregroundColor(.cBlack)

        Spacer()

        // 关闭按钮
        Button(action: dismiss) {
          Image(systemName: "xmark.circle.fill")
            .foregroundColor(.cBlack.opacity(0.2))
            .font(.system(size: 18, weight: .medium))
        }
      }
      .padding(.horizontal, 16)
      .padding(.top, 12)

      // MARK: 筛选选项列表
      ForEach(filterOptions, id: \.title) { option in
        HStack(spacing: 12) {
          // 选项图标
          Image(systemName: option.icon)
            .font(.system(size: 20))
            .foregroundColor(
              option.filter == selectedFilter ? .white : Color.cAccentBlue
            )
            .frame(width: 44, height: 44)
            .background(
              option.filter == selectedFilter
                ? Color.cAccentBlue : Color.cAccentBlue.opacity(0.1)
            )
            .cornerRadius(12)

          // 选项标题
          Text(option.title)
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(
              option.filter == selectedFilter ? Color.cAccentBlue : Color.cBlack
            )

          Spacer()

          // 选中状态标记
          if option.filter == selectedFilter {
            Image(systemName: "checkmark")
              .font(.system(size: 14, weight: .semibold))
              .foregroundColor(.cAccentBlue)
              .padding(.trailing, 12)
          }
        }
        .padding(4)
        .background(Color.cWhite)
        .cornerRadius(16)
        .overlay(
          RoundedRectangle(cornerRadius: 16)
            .strokeBorder(
              option.filter == selectedFilter
                ? Color.cAccentBlue : Color.cAccentBlue.opacity(0.08),
              lineWidth: 1
            )
        )
        .padding(.horizontal, 16)
        .onTapGesture {
          // 处理选项点击
          if let onFilterChanged = onFilterChanged {
            // 如果提供了回调，使用回调处理
            onFilterChanged(option.filter)
          } else {
            // 否则直接更新绑定值并关闭
            selectedFilter = option.filter
            dismiss()
          }
        }
      }

      Spacer()
    }

  }
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct CardFilterSheet_Previews: PreviewProvider {
    static var previews: some View {
      Group {
        // 显示所有卡片状态
        CardFilterSheet(
          selectedFilter: .constant(nil),
          dismiss: { print("关闭筛选弹窗") },
          onFilterChanged: { filter in
            print("筛选条件改变: \(filter?.description ?? "所有卡片")")
          }
        )
        .previewDisplayName("显示所有卡片")

        // 仅显示储蓄卡状态
        CardFilterSheet(
          selectedFilter: .constant(false),
          dismiss: { print("关闭筛选弹窗") },
          onFilterChanged: { filter in
            print("筛选条件改变: \(filter?.description ?? "所有卡片")")
          }
        )
        .previewDisplayName("仅显示储蓄卡")

        // 仅显示信用卡状态
        CardFilterSheet(
          selectedFilter: .constant(true),
          dismiss: { print("关闭筛选弹窗") },
          onFilterChanged: { filter in
            print("筛选条件改变: \(filter?.description ?? "所有卡片")")
          }
        )
        .previewDisplayName("仅显示信用卡")
      }
      .frame(height: 250)
      .background(Color.cLightBlue)
    }
  }
#endif
