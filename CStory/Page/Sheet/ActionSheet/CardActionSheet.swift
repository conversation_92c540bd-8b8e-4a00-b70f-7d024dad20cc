//
//  CardActionSheet.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

/// 卡片操作弹窗
///
/// 提供卡片相关的操作选项，包括编辑、删除等功能。
/// 支持两种删除模式：删除卡片及其所有交易记录，或仅删除卡片本身。
struct CardActionSheet: View {

  // MARK: - 属性

  /// 编辑卡片回调
  var onEditCard: () -> Void

  /// 删除卡片和交易记录回调
  var onDeleteWithTransactions: () -> Void

  /// 仅删除卡片回调
  var onDeleteCardOnly: () -> Void

  /// 关闭弹窗回调
  var dismiss: () -> Void

  // MARK: - 状态

  /// 数据管理器
  @Environment(\.dataManager) private var dataManager

  // MARK: - 计算属性

  /// 操作选项配置
  private var actionOptions:
    [(title: String, icon: String, isDestructive: Bool, action: () -> Void)]
  {
    [
      (
        "编辑卡片", "pencil", false,
        {
          dataManager.hapticManager.trigger(.impactLight)
          onEditCard()
          dismiss()
        }
      ),
      (
        "删除卡片和交易记录", "trash", true,
        {
          dataManager.hapticManager.trigger(.warning)
          onDeleteWithTransactions()
          dismiss()
        }
      ),
      (
        "仅删除卡片", "trash", true,
        {
          dataManager.hapticManager.trigger(.warning)
          onDeleteCardOnly()
          dismiss()
        }
      ),
    ]
  }

  // MARK: - 主体

  var body: some View {
    VStack(spacing: 12) {
      // MARK: 标题栏
      HStack {
        Text("卡片操作")
          .font(.system(size: 18, weight: .medium))
          .foregroundColor(.cBlack)

        Spacer()

        // 关闭按钮
        Button(action: {
          dataManager.hapticManager.trigger(.impactLight)
          dismiss()
        }) {
          Image(systemName: "xmark.circle.fill")
            .foregroundColor(.cBlack.opacity(0.2))
            .font(.system(size: 18, weight: .medium))
        }
      }
      .padding(.horizontal, 16)
      .padding(.top, 12)

      // MARK: 操作选项列表
      ForEach(Array(actionOptions.enumerated()), id: \.offset) { index, option in
        HStack(spacing: 12) {
          // 操作图标
          Image(systemName: option.icon)
            .font(.system(size: 20))
            .foregroundColor(option.isDestructive ? .white : Color.cAccentBlue)
            .frame(width: 44, height: 44)
            .background(
              option.isDestructive ? Color.cAccentRed : Color.cAccentBlue.opacity(0.1)
            )
            .cornerRadius(12)

          // 操作标题
          Text(option.title)
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(option.isDestructive ? Color.cAccentRed : Color.cBlack)

          Spacer()
        }
        .padding(4)
        .background(Color.cWhite)
        .cornerRadius(16)
        .overlay(
          RoundedRectangle(cornerRadius: 16)
            .strokeBorder(
              option.isDestructive
                ? Color.cAccentRed.opacity(0.2) : Color.cAccentBlue.opacity(0.08),
              lineWidth: 1
            )
        )
        .padding(.horizontal, 16)
        .onTapGesture {
          option.action()
        }
      }

      Spacer()
    }
  }
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct CardActionSheet_Previews: PreviewProvider {
    static var previews: some View {
      Group {
        // 储蓄卡操作预览
        CardActionSheet(
          onEditCard: { print("编辑储蓄卡") },
          onDeleteWithTransactions: { print("删除储蓄卡和交易记录") },
          onDeleteCardOnly: { print("仅删除储蓄卡") },
          dismiss: { print("关闭弹窗") }
        )
        .environment(\.dataManager, DataManagement())
        .previewDisplayName("储蓄卡操作")

        // 信用卡操作预览
        CardActionSheet(
          onEditCard: { print("编辑信用卡") },
          onDeleteWithTransactions: { print("删除信用卡和交易记录") },
          onDeleteCardOnly: { print("仅删除信用卡") },
          dismiss: { print("关闭弹窗") }
        )
        .environment(\.dataManager, DataManagement())
        .previewDisplayName("信用卡操作")
      }
      .frame(height: 300)
      .background(Color.cLightBlue)
    }
  }
#endif
