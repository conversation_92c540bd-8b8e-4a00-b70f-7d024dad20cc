//
//  CurrencyConversionView.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftData
import SwiftUI

/// 货币转换视图
///
/// 在创建交易时，当账单货币与卡片货币不同时，使用此视图进行货币转换。
/// 支持手动输入金额和汇率，或通过系统汇率自动计算。
///
/// ## 功能特性
/// - 支持多种交易类型
/// - 实时汇率计算
/// - 数字键盘输入
/// - 货币选择器
struct CurrencyConversionView: View {
  // MARK: - 环境属性

  @Environment(\.dismiss) private var dismiss
  @Environment(\.modelContext) private var modelContext
  @Environment(\.dataManager) private var dataManager

  // MARK: - 视图模型

  /// 使用状态对象来处理状态更新
  @StateObject private var viewModel = ConversionViewModel()

  // MARK: - 绑定属性

  /// 本位币代码
  @Binding var baseCurrencyCode: String
  /// 支出货币代码
  @Binding var expenseCurrencyCode: String
  /// 收入货币代码
  @Binding var incomeCurrencyCode: String
  /// 转账货币代码
  @Binding var transferCurrencyCode: String
  /// 交易金额
  @Binding var transactionAmount: String
  /// 视图显示状态
  @Binding var isSheetVisible: Bool
  /// 转换货币代码
  @Binding var convertCurrencyCode: String
  /// 转换汇率
  @Binding var conversionRate: String

  // MARK: - 属性

  /// 交易类型
  var transactionType: TransactionType

  /// 这些变量用于接收初始值
  var initialBillCurrencyCode: String = ""

  // MARK: - 状态属性

  /// 账单货币代码
  @State private var billCurrencyCode: String = ""
  /// 账单货币符号
  @State private var billCurrencyCodeSymbol: String = ""
  /// 转换货币选择器显示状态
  @State private var isConvertCurrencyPickerVisible: Bool = false
  /// 账单货币选择器显示状态
  @State private var isBillCurrencyPickerVisible: Bool = false

  // MARK: - 计算属性

  /// 获取卡片货币代码
  private var cardCurrencyCode: String {
    switch transactionType {
    case .expense:
      return expenseCurrencyCode
    case .income:
      return incomeCurrencyCode
    case .transfer:
      return transferCurrencyCode
    case .refund, .createCard, .adjustCard:
      return expenseCurrencyCode  // 默认使用支出货币代码
    }
  }

  // MARK: - 主体视图

  var body: some View {
    VStack(spacing: 0) {
      // 头部
      headerView

      // 主要内容区域
      ScrollView {
        VStack(spacing: 20) {
          // 货币转换卡片
          conversionCardView

          // 汇率信息卡片
          exchangeRateCardView

          Spacer(minLength: 20)
        }
        .padding(.horizontal, 16)
        .padding(.top, 20)
      }
    }
    .floatingSheet(
      isPresented: $isBillCurrencyPickerVisible,
      config: SheetBase(
        maxDetent: .fraction(0.7),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      SelectCurrencySheet(
        selectedCurrencyCode: $billCurrencyCode,
        currencySymbol: $billCurrencyCodeSymbol,
        mode: "all",
        onSelect: { _ in updateExchangeRate() }
      )
    }
    .floatingSheet(
      isPresented: $isConvertCurrencyPickerVisible,
      config: SheetBase(
        maxDetent: .height(300),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      SelectCurrencySheet(
        selectedCurrencyCode: $viewModel.convertCurrencyCode,
        currencySymbol: $viewModel.convertCurrencyCodeSymbol,
        mode: "simpleConversion",
        onSelect: { _ in updateExchangeRate() },
        baseCurrencyCode: baseCurrencyCode,
        alternativeCurrencyCode: cardCurrencyCode
      )
    }
    .floatingSheet(
      isPresented: $viewModel.isNumericKeypadVisible,
      config: SheetBase(
        maxDetent: .height(314),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      numericKeypadView()
    }
    .onAppear {
      // 根据交易类型设置初始值
      setupInitialValues()
    }
    .onChange(of: billCurrencyCode) { updateExchangeRate() }
    .onChange(of: viewModel.convertCurrencyCode) { updateExchangeRate() }
  }

  // 根据交易类型设置初始值
  private func setupInitialValues() {
    // 设置账单金额为当前交易金额
    viewModel.billAmount = transactionAmount

    // 设置初始账单货币代码
    if !initialBillCurrencyCode.isEmpty {
      billCurrencyCode = initialBillCurrencyCode
    } else {
      switch transactionType {
      case .expense:
        billCurrencyCode = expenseCurrencyCode
      case .income:
        billCurrencyCode = incomeCurrencyCode
      case .transfer:
        billCurrencyCode = transferCurrencyCode
      case .refund, .createCard, .adjustCard:
        billCurrencyCode = expenseCurrencyCode  // 默认使用支出货币代码
      }
    }

    // 始终设置初始换算货币为本位币
    viewModel.convertCurrencyCode = baseCurrencyCode

    // 设置换算货币符号
    if let currency = dataManager.currencies.first(where: {
      $0.code == viewModel.convertCurrencyCode
    }) {
      viewModel.convertCurrencyCodeSymbol = currency.symbol
    }

    // 更新汇率
    updateExchangeRate()
  }

  // MARK: - UI Components

  /// 货币转换卡片视图
  private var conversionCardView: some View {
    VStack(spacing: 16) {
      // 账单货币输入
      currencyAmountRow(
        title: "账单金额",
        currencyCode: billCurrencyCode,
        currencySymbol: billCurrencyCodeSymbol,
        amount: viewModel.billAmount.isEmpty ? "0" : viewModel.billAmount,
        onSelectCurrency: { isBillCurrencyPickerVisible = true },
        onSelectAmount: {
          viewModel.activeField = .billAmount
          viewModel.tempAmount = viewModel.billAmount.isEmpty ? "0" : viewModel.billAmount
          viewModel.isNumericKeypadVisible = true
        }
      )

      // 转换箭头
      conversionArrowView

      // 换算结果
      currencyAmountRow(
        title: "换算金额",
        currencyCode: viewModel.convertCurrencyCode,
        currencySymbol: getCurrencySymbol(for: viewModel.convertCurrencyCode),
        amount: viewModel.convertAmount.isEmpty
          ? viewModel.calculateConvertAmount() : viewModel.convertAmount,
        onSelectCurrency: { isConvertCurrencyPickerVisible = true },
        onSelectAmount: {
          viewModel.activeField = .convertAmount
          viewModel.tempAmount = viewModel.convertAmount.isEmpty ? "0" : viewModel.convertAmount
          viewModel.isNumericKeypadVisible = true
        }
      )
    }
    .padding(20)
    .background(Color.cWhite)
    .cornerRadius(16)
    .shadow(color: Color.cBlack.opacity(0.04), radius: 8, x: 0, y: 2)
  }

  /// 汇率信息卡片视图
  private var exchangeRateCardView: some View {
    VStack(spacing: 16) {
      HStack {
        Text("汇率信息")
          .font(.system(size: 16, weight: .semibold))
          .foregroundColor(.cBlack)

        Spacer()

        Button(action: {
          dataManager.hapticManager.trigger(.impactLight)
          withAnimation(.easeInOut(duration: 0.2)) {
            updateExchangeRate()
          }
        }) {
          HStack(spacing: 6) {
            Image(systemName: "arrow.clockwise")
              .font(.system(size: 12, weight: .medium))
            Text("更新")
              .font(.system(size: 12, weight: .medium))
          }
          .foregroundColor(.cAccentBlue)
        }
      }

      // 汇率显示
      HStack {
        Text("1 \(billCurrencyCode)")
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.cBlack.opacity(0.8))

        Image(systemName: "equal")
          .font(.system(size: 12, weight: .medium))
          .foregroundColor(.cBlack.opacity(0.6))

        Button(action: {
          dataManager.hapticManager.trigger(.impactLight)
          viewModel.activeField = .conversionRate
          viewModel.tempAmount = viewModel.conversionRate.isEmpty ? "1.0" : viewModel.conversionRate
          viewModel.isNumericKeypadVisible = true
        }) {
          Text(
            "\(viewModel.conversionRate.isEmpty ? "1.0" : viewModel.conversionRate) \(viewModel.convertCurrencyCode)"
          )
          .font(.system(size: 14, weight: .semibold))
          .foregroundColor(.cAccentBlue)
          .underline()
        }

        Spacer()
      }
    }
    .padding(20)
    .background(Color.cWhite)
    .cornerRadius(16)
    .shadow(color: Color.cBlack.opacity(0.04), radius: 8, x: 0, y: 2)
  }

  /// 转换箭头视图
  private var conversionArrowView: some View {
    HStack {
      Spacer()

      VStack(spacing: 4) {
        Image(systemName: "arrow.down")
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(.cAccentBlue)

        Text("换算")
          .font(.system(size: 11, weight: .medium))
          .foregroundColor(.cBlack.opacity(0.6))
      }

      Spacer()
    }
  }

  private var headerView: some View {
    HStack {
      Button(action: {
        dataManager.hapticManager.trigger(.impactLight)
        isSheetVisible = false
      }) {
        Image(systemName: "xmark")
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(.cBlack.opacity(0.7))
          .frame(width: 44, height: 44)
          .background(Color.cBeige.opacity(0.3))
          .cornerRadius(22)
      }

      Spacer()

      VStack(spacing: 2) {
        Text("货币换算")
          .font(.system(size: 18, weight: .semibold))
          .foregroundColor(.cBlack)

        Text("\(billCurrencyCode) → \(viewModel.convertCurrencyCode)")
          .font(.system(size: 12, weight: .medium))
          .foregroundColor(.cBlack.opacity(0.6))
      }

      Spacer()

      Button(action: {
        dataManager.hapticManager.trigger(.impactMedium)
        saveAndDismiss()
      }) {
        Text("完成")
          .font(.system(size: 16, weight: .semibold))
          .foregroundColor(.cWhite)
          .padding(.horizontal, 20)
          .padding(.vertical, 10)
          .background(Color.cAccentBlue)
          .cornerRadius(22)
      }
    }
    .padding(.horizontal, 20)
    .padding(.vertical, 16)
    .background(Color.cWhite)
    .shadow(color: Color.cBlack.opacity(0.05), radius: 1, x: 0, y: 1)
  }

  /// 货币金额输入行
  private func currencyAmountRow(
    title: String,
    currencyCode: String,
    currencySymbol: String,
    amount: String,
    onSelectCurrency: @escaping () -> Void,
    onSelectAmount: @escaping () -> Void
  ) -> some View {
    VStack(spacing: 12) {
      // 标题
      HStack {
        Text(title)
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.cBlack.opacity(0.8))
        Spacer()
      }

      // 货币和金额输入区域
      HStack(spacing: 12) {
        // 货币选择按钮
        Button(action: {
          dataManager.hapticManager.trigger(.impactLight)
          onSelectCurrency()
        }) {
          HStack(spacing: 8) {
            Text(currencySymbol)
              .font(.system(size: 18, weight: .semibold))
              .foregroundColor(.cBlack)

            Text(currencyCode)
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(.cBlack.opacity(0.7))

            Image(systemName: "chevron.down")
              .font(.system(size: 12, weight: .medium))
              .foregroundColor(.cBlack.opacity(0.5))
          }
          .padding(.horizontal, 16)
          .padding(.vertical, 12)
          .background(Color.cBeige.opacity(0.3))
          .cornerRadius(12)
        }

        // 金额输入按钮
        Button(action: {
          dataManager.hapticManager.trigger(.impactLight)
          onSelectAmount()
        }) {
          HStack {
            Spacer()
            Text(amount)
              .font(.system(size: 20, weight: .semibold))
              .foregroundColor(.cBlack)
              .multilineTextAlignment(.trailing)
          }
          .padding(.horizontal, 16)
          .padding(.vertical, 12)
          .background(Color.cBeige.opacity(0.1))
          .cornerRadius(12)
        }
      }
    }
  }

  /// 获取货币符号
  private func getCurrencySymbol(for code: String) -> String {
    return dataManager.currencies.first { $0.code == code }?.symbol ?? "¥"
  }

  private func amountButton(amount: String, onTap: @escaping () -> Void) -> some View {
    Button(action: onTap) {
      Text(amount)
        .font(.system(size: 15, weight: .regular))
        .foregroundColor(.cBlack)
        .frame(maxWidth: .infinity, alignment: .trailing)
        .padding(.horizontal, 16)
    }
    .frame(height: 56)
    .background(Color.cWhite)
    .cornerRadius(16)
    .overlay(
      RoundedRectangle(cornerRadius: 16)
        .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
    )
  }

  // 数字键盘视图
  private func numericKeypadView() -> some View {
    NumericKeypad(
      text: $viewModel.tempAmount,
      onSave: {
        viewModel.saveNumericInput()
      },
      allowNegative: false,
      maxDecimalPlaces: 6  // 汇率支持6位小数
    )
    .background(Color.cBeige)
  }

  // MARK: - Exchange Rate Logic

  private func updateExchangeRate() {
    guard !billCurrencyCode.isEmpty && !viewModel.convertCurrencyCode.isEmpty else { return }

    guard let sourceCurrency = dataManager.currencies.first(where: { $0.code == billCurrencyCode }),
      let targetCurrency = dataManager.currencies.first(where: {
        $0.code == viewModel.convertCurrencyCode
      })
    else { return }

    // 计算并设置汇率
    if sourceCurrency.code == targetCurrency.code {
      viewModel.conversionRate = "1"
    } else if sourceCurrency.code == baseCurrencyCode {
      let rate = 1.0 / targetCurrency.rate
      viewModel.conversionRate = viewModel.formatNumberString(rate, precision: 6)
    } else if targetCurrency.code == baseCurrencyCode {
      viewModel.conversionRate = viewModel.formatNumberString(sourceCurrency.rate, precision: 6)
    } else {
      let rate = sourceCurrency.rate / targetCurrency.rate
      viewModel.conversionRate = viewModel.formatNumberString(rate, precision: 6)
    }

    // 更新换算金额（如果已有账单金额）
    if !viewModel.billAmount.isEmpty && viewModel.billAmount != "0" {
      viewModel.updateConvertAmount()
    }
  }

  // MARK: - Save and Dismiss

  private func saveAndDismiss() {
    // 更新交易金额
    if !viewModel.billAmount.isEmpty && viewModel.billAmount != "0" {
      transactionAmount = viewModel.billAmount
    }

    // 根据交易类型更新对应的货币代码
    switch transactionType {
    case .expense:
      expenseCurrencyCode = billCurrencyCode
    case .income:
      incomeCurrencyCode = billCurrencyCode
    case .transfer:
      transferCurrencyCode = billCurrencyCode
    case .refund, .createCard, .adjustCard:
      expenseCurrencyCode = billCurrencyCode  // 默认更新支出货币代码
    }

    // 更新换算货币代码
    convertCurrencyCode = viewModel.convertCurrencyCode
    conversionRate = viewModel.conversionRate

    // 关闭货币转换视图
    isSheetVisible = false
  }
}

// MARK: - 输入字段枚举
enum InputField {
  case none
  case billAmount
  case conversionRate
  case convertAmount
}

// MARK: - 转换视图模型
class ConversionViewModel: ObservableObject {

  // 发布状态
  @Published var billAmount: String = ""
  @Published var conversionRate: String = "1.0"
  @Published var convertAmount: String = ""
  @Published var convertCurrencyCode: String = ""
  @Published var convertCurrencyCodeSymbol: String = ""

  // 键盘相关状态
  @Published var tempAmount: String = "0"
  @Published var isNumericKeypadVisible: Bool = false
  @Published var activeField: InputField = .none

  // 保存数字键盘输入
  func saveNumericInput() {
    switch activeField {
    case .billAmount:
      let oldValue = billAmount
      billAmount = formatAmount(tempAmount)
      if oldValue != billAmount {
        updateConvertAmount()
      }

    case .conversionRate:
      let oldValue = conversionRate
      conversionRate = formatRate(tempAmount)
      if oldValue != conversionRate {
        updateConvertAmount()
      }

    case .convertAmount:
      let oldValue = convertAmount
      convertAmount = formatAmount(tempAmount)
      if oldValue != convertAmount && billAmount != "0" && !billAmount.isEmpty {
        updateConversionRate()
      }

    case .none:
      break
    }

    isNumericKeypadVisible = false
    activeField = .none
  }

  // 格式化金额
  func formatAmount(_ value: String) -> String {
    if let doubleValue = Double(value), doubleValue > 0 {
      return NumberFormatService.shared.formatAmountForInput(doubleValue, maxDecimals: 2)
    }
    return "0"
  }

  // 格式化汇率
  func formatRate(_ value: String) -> String {
    if let doubleValue = Double(value), doubleValue > 0 {
      return NumberFormatService.shared.formatExchangeRate(doubleValue, maxDecimals: 6)
    }
    return "1"
  }

  // 通用数字格式化（去掉尾部的0和不需要的小数点）
  func formatNumberString(_ value: Double, precision: Int) -> String {
    return NumberFormatService.shared.formatAmountForInput(value, maxDecimals: precision)
  }

  // 移除尾部的0和不需要的小数点
  func removeTrailingZeros(_ value: String) -> String {
    var result = value
    if result.contains(".") {
      while result.hasSuffix("0") {
        result.removeLast()
      }
      if result.hasSuffix(".") {
        result.removeLast()
      }
    }
    return result
  }

  // 计算并返回换算金额
  func calculateConvertAmount() -> String {
    if let billValue = Double(billAmount),
      let rateValue = Double(conversionRate),
      billValue > 0
    {
      let result = billValue * rateValue
      return formatNumberString(result, precision: 2)
    }
    return "0"
  }

  // 更新换算金额
  func updateConvertAmount() {
    convertAmount = calculateConvertAmount()
  }

  // 根据账单金额和换算金额计算并更新汇率
  func updateConversionRate() {
    if let billValue = Double(billAmount),
      let convertValue = Double(convertAmount),
      billValue > 0
    {
      let newRate = convertValue / billValue
      conversionRate = formatRate(String(newRate))
    } else if let convertValue = Double(convertAmount),
      convertValue > 0,
      billAmount == "0" || billAmount.isEmpty
    {
      billAmount = "1"
      conversionRate = convertAmount
    }
  }
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct CurrencyConversionView_Previews: PreviewProvider {
    static var previews: some View {
      CurrencyConversionPreviewContainer()
    }
  }

  struct CurrencyConversionPreviewContainer: View {
    @State private var showSheet = false
    @State private var baseCurrencyCode = "CNY"
    @State private var expenseCurrencyCode = "USD"
    @State private var incomeCurrencyCode = "CNY"
    @State private var transferCurrencyCode = "CNY"
    @State private var transactionAmount = "100"
    @State private var isSheetVisible = true
    @State private var convertCurrencyCode = "CNY"
    @State private var conversionRate = "7.2"

    var body: some View {
      VStack {
        Button("显示货币转换") {
          showSheet = true
        }
        .padding()
        .background(Color.cAccentBlue)
        .foregroundColor(.white)
        .cornerRadius(12)
      }
      .frame(maxWidth: .infinity, maxHeight: .infinity)
      .background(Color.cLightBlue)
      .floatingSheet(
        isPresented: $showSheet,
        config: SheetBase(
          maxDetent: .fraction(0.9),
          cornerRadius: 24,
          interactiveDimiss: true,
          hPadding: 8,
          bPadding: 4
        )
      ) {
        CurrencyConversionView(
          baseCurrencyCode: $baseCurrencyCode,
          expenseCurrencyCode: $expenseCurrencyCode,
          incomeCurrencyCode: $incomeCurrencyCode,
          transferCurrencyCode: $transferCurrencyCode,
          transactionAmount: $transactionAmount,
          isSheetVisible: $isSheetVisible,
          convertCurrencyCode: $convertCurrencyCode,
          conversionRate: $conversionRate,
          transactionType: .expense
        )
        .environment(\.dataManager, DataManagement())
      }
    }
  }
#endif
