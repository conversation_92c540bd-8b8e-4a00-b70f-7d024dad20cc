//
//  TransactionListContent.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/20.
//

import SwiftUI

/// 统一的交易列表内容组件
///
/// 使用新架构的TransactionRowVM数据源，为所有交易列表页面提供统一的显示组件。
/// 支持HomeView、TransactionRecordView、CardDetailView等页面。
///
/// ## 使用示例
/// ```swift
/// // 方式1: 使用预先准备好的ViewModel（推荐）
/// let viewModel = TransactionListContentVM(
///     transactionDayGroups: dayGroups,
///     currencySymbol: "¥"
/// )
/// TransactionListContent(viewModel: viewModel)
///
/// // 方式2: 直接传入数据参数
/// TransactionListContent(
///     transactionDayGroups: dayGroups,
///     currencySymbol: "¥"
/// )
/// ```
struct TransactionListContent: View {

  // MARK: - ViewModel

  /// 交易列表内容视图模型
  @ObservedObject var viewModel: TransactionListContentVM

  // MARK: - 初始化

  /// 使用预先准备好的ViewModel初始化（推荐使用）
  init(viewModel: TransactionListContentVM) {
    self.viewModel = viewModel
  }

  /// 直接使用数据参数初始化
  init(
    transactionDayGroups: [TransactionDayGroupWithRowVM],
    currencySymbol: String
  ) {
    self.viewModel = TransactionListContentVM(
      transactionDayGroups: transactionDayGroups,
      currencySymbol: currencySymbol
    )
  }

  // MARK: - 主体视图

  var body: some View {
    LazyVStack(spacing: 12) {
      if !viewModel.transactionDayGroups.isEmpty {
        // 交易记录区域作为整体进行动画
        VStack(spacing: 12) {
          // 新架构：使用TransactionRowVM
          ForEach(viewModel.transactionDayGroups) { dayGroup in
            TransactionDayGroupView(
              dateText: dayGroup.dateText,
              income: dayGroup.dayIncome,
              expense: dayGroup.dayExpense,
              currencySymbol: viewModel.currencySymbol,
              content: {
                VStack(spacing: 0) {
                  ForEach(Array(dayGroup.transactionRowVMs.enumerated()), id: \.offset) {
                    index, transactionRowVM in
                    TransactionRow(viewModel: transactionRowVM)
                      .padding(.horizontal, 16)
                      .padding(.bottom, index == dayGroup.transactionRowVMs.count - 1 ? 0 : 12)
                  }
                }
              }
            )
          }

          // 底部提示
          Text("- 没有更多记录了 -")
                .font(.system(size: 12, weight: .regular))
            .foregroundColor(.cBlack.opacity(0.4))
            .padding(.bottom, 12)
        }
        .transition(
          .asymmetric(
            insertion: .move(edge: .trailing).combined(with: .opacity),
            removal: .move(edge: .leading).combined(with: .opacity)
          )
        )
      } else {
        // 空状态视图

        VStack(spacing: 12) {
          Image("404")
            .resizable()
            .frame(width: 80, height: 80)
          Text("暂无最近交易")
            .font(.system(size: 14, weight: .regular))
            .foregroundColor(.cBlack.opacity(0.4))
        }
        .frame(height: 120)
        .frame(maxWidth: .infinity)

        .transition(
          .asymmetric(
            insertion: .move(edge: .leading).combined(with: .opacity),
            removal: .move(edge: .trailing).combined(with: .opacity)
          )
        )
      }
    }
  }
}

// MARK: - 预览

#Preview("基本使用") {
  // 创建模拟交易数据
  let mockTransactionRows = [
    TransactionRowVM(
      icon: .emoji("🛍️"),
      categoryName: "购物-日常用品",
      formattedTime: "14:35",
      displayAmount: -128.50,
      amountColor: .black,
      currencySymbol: "¥",
      hasRefundTag: false,
      hasDiscountTag: false
    ),
    TransactionRowVM(
      icon: .emoji("💰"),
      categoryName: "职业收入-工资",
      formattedTime: "09:00",
      displayAmount: 8000.00,
      amountColor: .black,
      currencySymbol: "¥",
      hasRefundTag: false,
      hasDiscountTag: false
    ),
    TransactionRowVM(
      icon: .emoji("🍕"),
      categoryName: "餐饮-外卖",
      formattedTime: "12:30",
      displayAmount: -45.00,
      amountColor: .black,
      currencySymbol: "¥",
      hasRefundTag: false,
      hasDiscountTag: true
    )
  ]
  
  let mockDayGroups = [
    TransactionDayGroupWithRowVM(
      dateText: "今天",
      dayIncome: 8000.00,
      dayExpense: 173.50,
      transactionRowVMs: mockTransactionRows
    )
  ]
  
  TransactionListContent(
    transactionDayGroups: mockDayGroups,
    currencySymbol: "¥"
  )
  
  .background(Color.gray.opacity(0.1))
}

#Preview("空状态") {
  TransactionListContent(
    transactionDayGroups: [],
    currencySymbol: "¥"
  )
  
  .background(Color.gray.opacity(0.1))
}

#Preview("多日期分组") {
  let todayTransactions = [
    TransactionRowVM(
      icon: .emoji("🛍️"),
      categoryName: "购物-日常用品",
      formattedTime: "14:35",
      displayAmount: -128.50,
      amountColor: .black,
      currencySymbol: "¥",
      hasRefundTag: false,
      hasDiscountTag: false
    )
  ]
  
  let yesterdayTransactions = [
    TransactionRowVM(
      icon: .emoji("💰"),
      categoryName: "职业收入-工资",
      formattedTime: "09:00",
      displayAmount: 8000.00,
      amountColor: .black,
      currencySymbol: "¥",
      hasRefundTag: false,
      hasDiscountTag: false
    ),
    TransactionRowVM(
      icon: .emoji("🍕"),
      categoryName: "餐饮-外卖",
      formattedTime: "12:30",
      displayAmount: -45.00,
      amountColor: .black,
      currencySymbol: "¥",
      hasRefundTag: true,
      hasDiscountTag: false
    )
  ]
  
  let mockDayGroups = [
    TransactionDayGroupWithRowVM(
      dateText: "今天",
      dayIncome: 0.00,
      dayExpense: 128.50,
      transactionRowVMs: todayTransactions
    ),
    TransactionDayGroupWithRowVM(
      dateText: "昨天",
      dayIncome: 8000.00,
      dayExpense: 45.00,
      transactionRowVMs: yesterdayTransactions
    )
  ]
  
  ScrollView {
    TransactionListContent(
      transactionDayGroups: mockDayGroups,
      currencySymbol: "¥"
    )
    
  }
  .background(Color.gray.opacity(0.1))
}

// MARK: - 子组件

/// 交易日期分组视图（新架构专用）
private struct TransactionDayGroupView<Content: View>: View {
  let dateText: String
  let income: Double
  let expense: Double
  let currencySymbol: String
  let content: () -> Content

  var body: some View {
    VStack(spacing: 12) {
      // 日期头部
        HStack(spacing:8) {
        Text(dateText)
          .font(.system(size: 14, weight: .regular))
          .foregroundColor(.cBlack.opacity(0.4))
        Spacer()

        if income > 0 {
          HStack(spacing: 2) {
            Text("收")
              .font(.system(size: 12, weight: .medium))
              .foregroundColor(.white)
              .padding(.horizontal, 4)
              .padding(.vertical, 1)
              .background(Color.cAccentGreen)
              .cornerRadius(4)
            DisplayCurrencyView.size12(
              symbol: currencySymbol,
              amount: income
            )
            .simpleFormat()
            .color(.cAccentGreen)
          }
        }

        if expense > 0 {
          HStack(spacing: 2) {
            Text("支")
              .font(.system(size: 12, weight: .medium))
              .foregroundColor(.white)
              .padding(.horizontal, 4)
              .padding(.vertical, 1)
              .background(Color.cAccentRed)
              .cornerRadius(4)
            DisplayCurrencyView.size12(
              symbol: currencySymbol,
              amount: expense
            )
            .simpleFormat()
            .color(.cAccentRed)
          }
        }
      }
      .padding(.horizontal, 16)

      // 交易内容
      content()
    }
    .padding(.bottom, 8)
  }
}
