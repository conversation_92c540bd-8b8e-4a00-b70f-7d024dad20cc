//
//  CardRow.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/18.
//

import SwiftUI

/// 银行卡信息行视图组件
///
/// 显示银行卡基本信息的可交互行组件，遵循MVVM架构模式。
/// 通过CardRowVM处理所有数据格式化和业务逻辑，视图专注于UI渲染。
///
/// 支持显示银行logo、卡片名称、银行名称、余额信息，并提供选中状态、
/// 类型标签和额外信息的可选显示。组件采用响应式设计，适配不同屏幕尺寸。
///
/// ## 核心功能
/// - 银行卡信息可视化展示
/// - 选中状态的视觉反馈
/// - 点击交互和触觉反馈
/// - 信用卡和储蓄卡类型区分
/// - 多货币余额显示支持
///
/// ## 使用示例
/// ```swift
/// // 基本使用
/// let viewModel = CardRowVM(from: cardModel)
/// viewModel.onTap = { handleCardSelection() }
/// CardRow(viewModel: viewModel)
///
/// // 选中状态卡片
/// let selectedVM = CardRowVM(
///   from: cardModel,
///   isSelected: true,
///   showTypeTag: true
/// )
/// CardRow(viewModel: selectedVM)
/// ```
///
/// - Author: 咩咩
/// - Since: 2025.7.18
/// - Note: 该组件是无状态的，所有状态管理由ViewModel负责
/// - SeeAlso: `CardRowVM`, `DisplayCurrencyView`
struct CardRow: View {

  // MARK: - Properties

  /// 卡片行视图模型
  ///
  /// 管理卡片显示数据和交互逻辑的视图模型实例。
  /// 包含卡片名称、余额、选中状态等所有显示相关信息。
  @ObservedObject var viewModel: CardRowVM
  
  /// 数据管理器
  @Environment(\.dataManager) private var dataManager

  // MARK: - Body

  var body: some View {
    Button(action: {
      dataManager.hapticManager.trigger(.selection)
      viewModel.onTap?()
    }) {
      HStack(alignment: .center) {
        // MARK: 左侧内容 (银行logo和卡片信息)
        HStack(spacing: 12) {
          // 银行logo
          IconView(
            viewModel: IconViewVM.optionalImage(
              viewModel.bankLogo,
              size: 40,
              style: IconStyle(
                backgroundColor: Color.cAccentBlue.opacity(0.1),
                cornerRadius: 12
              )
            )
          )

          VStack(alignment: .leading, spacing: 4) {
            HStack(alignment: .center, spacing: 4) {
              Text(viewModel.cardName)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.cBlack)

              if viewModel.hasTypeTag {
                TagView(
                  text: viewModel.cardTypeText,
                  color: viewModel.isCredit ? .orange : .blue
                )
              }
            }

            Text(viewModel.bankName)
              .font(.system(size: 12, weight: .regular))
              .foregroundColor(.cBlack.opacity(0.6))
          }
        }

        Spacer()

        // MARK: 右侧内容 (余额和附加信息)
        VStack(alignment: .trailing, spacing: 4) {
          // 余额显示
          DisplayCurrencyView.size15(
            symbol: viewModel.currencySymbol,
            amount: viewModel.balance
          )
          .simpleFormat()
          .color(viewModel.balanceColor)

          // 附加信息
          if let additionalInfo = viewModel.additionalInfo {
            Text(additionalInfo)
              .font(.system(size: 12, weight: .regular))
              .foregroundColor(.cBlack.opacity(0.4))
          }
        }
      }
      .padding(12)
      .background(Color.cWhite)
      .cornerRadius(16)
      .overlay(
        RoundedRectangle(cornerRadius: 16)
          .strokeBorder(
            viewModel.isSelected ? Color.cAccentBlue : Color.cAccentBlue.opacity(0.08),
            lineWidth: viewModel.isSelected ? 1.5 : 1
          )
          .animation(.easeInOut(duration: 0.2), value: viewModel.isSelected)
      )
      .animation(.easeInOut(duration: 0.2), value: viewModel.isSelected)
    }
  }
}

// MARK: - 私有组件

/// 卡片类型标签视图
/// 
/// 显示卡片类型标识的小型标签组件。
/// 支持信用卡和储蓄卡等不同类型的颜色区分。
/// 
/// - Parameters:
///   - text: 标签显示文字
///   - color: 标签背景颜色主题
/// - Returns: 配置好的标签视图
/// - Note: 该组件为私有组件，仅在CardRow内部使用
private func TagView(text: String, color: Color) -> some View {
  Text(text)
    .font(.system(size: 10, weight: .medium))
    .foregroundColor(.white)
    .padding(.horizontal, 4)
    .padding(.vertical, 1)
    .background(color.opacity(0.7))
    .cornerRadius(4)
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct CardRow_Previews: PreviewProvider {
    static var previews: some View {
      ScrollView {
        VStack(spacing: 10) {
          Text("卡片行预览").font(.largeTitle).bold().padding()

          // 场景1: 普通储蓄卡
          CardRow(
            viewModel: .init(
              cardName: "招商银行储蓄卡",
              bankName: "招商银行",
              balance: 12580.50,
              currencySymbol: "¥",
              isCredit: false,
              hasTypeTag: true,
              isSelected: false
            ))

          // 场景2: 信用卡
          CardRow(
            viewModel: .init(
              cardName: "工行信用卡",
              bankName: "中国工商银行",
              balance: -2500.00,
              currencySymbol: "¥",
              isCredit: true,
              hasTypeTag: true,
              isSelected: false,
              additionalInfo: "额度: ¥10000"
            ))

          // 场景3: 选中状态的卡片
          CardRow(
            viewModel: .init(
              cardName: "支付宝余额",
              bankName: "支付宝",
              balance: 1024.88,
              currencySymbol: "¥",
              isCredit: false,
              hasTypeTag: false,
              isSelected: true
            ))

          // 场景4: 外币卡片
          CardRow(
            viewModel: .init(
              cardName: "美元储蓄卡",
              bankName: "中国银行",
              balance: 500.25,
              currencySymbol: "$",
              isCredit: false,
              hasTypeTag: true,
              isSelected: false,
              additionalInfo: "外币账户"
            ))

        }
        .padding(.horizontal)
      }
      .background(Color(.systemGroupedBackground))
    }
  }
#endif
