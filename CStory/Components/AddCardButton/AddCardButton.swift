//
//  AddCardButton.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

/// 添加卡片按钮组件
///
/// 用于在不同界面中显示添加新卡片的入口按钮。
/// 支持多种样式配置，采用MVVM架构。
///
/// ## 使用示例
/// ```swift
/// // 标准样式（用于列表中）
/// AddCardButton(viewModel: AddCardButtonVM.standard {
///   pathManager.path.append(NavigationDestination.cardCategoryView)
/// })
///
/// // 最小化样式（用于卡片网格中）
/// AddCardButton(viewModel: AddCardButtonVM.minimal {
///   pathManager.path.append(NavigationDestination.cardCategoryView)
/// })
/// ```
struct AddCardButton: View {
  
  // MARK: - 属性
  
  /// 视图模型
  @ObservedObject private var viewModel: AddCardButtonVM
  
  // MARK: - 状态
  
  @Environment(\.dismiss) private var dismiss
  /// 数据管理器
  @Environment(\.dataManager) private var dataManager
  
  // MARK: - 初始化
  
  /// 初始化添加卡片按钮
  /// - Parameter viewModel: 视图模型
  init(viewModel: AddCardButtonVM) {
    self.viewModel = viewModel
  }
  
  // MARK: - 主体视图
  
  var body: some View {
    Button(action: {
      dataManager.hapticManager.trigger(.impactMedium)
      dismiss()
      viewModel.action()
    }) {
      buttonContent
    }
    .buttonStyle(PlainButtonStyle())
  }
  
  // MARK: - 子视图
  
  /// 按钮内容
  private var buttonContent: some View {
    Group {
      if viewModel.style.showText {
        // 标准样式：显示图标和文本
        HStack(spacing: viewModel.style.spacing) {
          iconView
          Text(viewModel.style.text)
            .font(.system(size: viewModel.style.fontSize, weight: viewModel.style.fontWeight))
        }
        .foregroundStyle(viewModel.style.foregroundColor)
        .frame(maxWidth: .infinity)
        .padding(.vertical, viewModel.style.verticalPadding)
        .background(viewModel.style.backgroundColor)
        .cornerRadius(viewModel.style.cornerRadius)
        .overlay(
          RoundedRectangle(cornerRadius: viewModel.style.cornerRadius)
            .strokeBorder(
              viewModel.style.foregroundColor,
              style: viewModel.style.strokeStyle
            )
        )
      } else {
        // 最小化样式：仅显示图标
        VStack {
          iconView
        }
        .foregroundColor(viewModel.style.foregroundColor)
        .frame(
          width: viewModel.style.width,
          height: viewModel.style.height
        )
        .cornerRadius(viewModel.style.cornerRadius)
        .overlay(
          RoundedRectangle(cornerRadius: viewModel.style.cornerRadius)
            .strokeBorder(
              viewModel.style.foregroundColor,
              style: viewModel.style.strokeStyle
            )
        )
      }
    }
  }
  
  /// 图标视图
  private var iconView: some View {
    Group {
      if viewModel.style.iconName == "plus" {
        // 系统图标
        Image(systemName: viewModel.style.iconName)
          .font(.system(size: viewModel.style.iconSize, weight: .medium))
      } else {
        // 自定义图标
        Image(viewModel.style.iconName)
          .font(.system(size: viewModel.style.iconSize))
          .frame(width: 24, height: 24, alignment: .center)
      }
    }
  }
}

// MARK: - 便捷初始化方法

extension AddCardButton {
  
  /// 创建标准样式的添加卡片按钮（向后兼容）
  /// - Parameter pathManager: 路径管理器
  /// - Returns: 添加卡片按钮视图
  static func standard(pathManager: PathManagerHelper) -> AddCardButton {
    return AddCardButton(
      viewModel: AddCardButtonVM.standard {
        pathManager.path.append(NavigationDestination.cardCategoryView)
      }
    )
  }
  
  /// 创建最小化样式的添加卡片按钮
  /// - Parameter pathManager: 路径管理器
  /// - Returns: 添加卡片按钮视图
  static func minimal(pathManager: PathManagerHelper) -> AddCardButton {
    return AddCardButton(
      viewModel: AddCardButtonVM.minimal {
        pathManager.path.append(NavigationDestination.cardCategoryView)
      }
    )
  }
}

// MARK: - 预览

#Preview {
  VStack(spacing: 20) {
    // 标准样式
    AddCardButton(
      viewModel: AddCardButtonVM.standard {
        print("Standard button tapped")
      }
    )
    .padding()
    
    // 最小化样式
    HStack {
      AddCardButton(
        viewModel: AddCardButtonVM.minimal {
          print("Minimal button tapped")
        }
      )
      Spacer()
    }
    .padding()
  }
  .background(Color.gray.opacity(0.1))
}